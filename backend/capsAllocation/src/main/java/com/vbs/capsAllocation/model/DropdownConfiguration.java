package com.vbs.capsAllocation.model;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.time.LocalDateTime;

@Entity
@Table(name = "dropdown_configurations", 
       uniqueConstraints = {@UniqueConstraint(columnNames = {"dropdown_type", "option_value"})})
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DropdownConfiguration {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(name = "dropdown_type", nullable = false)
    private String dropdownType; // e.g., "PROJECT", "VENDOR", "PROCESS", "SHIFT"
    
    @Column(name = "option_value", nullable = false)
    private String optionValue; // The actual dropdown option value
    
    @Column(name = "display_name", nullable = false)
    private String displayName; // Display name for the option (can be same as optionValue)
    
    @Column(name = "is_active", nullable = false)
    private Boolean isActive = true;
    
    @Column(name = "sort_order")
    private Integer sortOrder = 0; // For ordering dropdown options
    
    @ManyToOne
    @JoinColumn(name = "created_by", nullable = false)
    private User createdBy;
    
    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt;
    
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;
    
    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
        updatedAt = LocalDateTime.now();
    }
    
    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
    }
    
    public DropdownConfiguration(String dropdownType, String optionValue, String displayName, User createdBy) {
        this.dropdownType = dropdownType;
        this.optionValue = optionValue;
        this.displayName = displayName;
        this.createdBy = createdBy;
        this.isActive = true;
        this.sortOrder = 0;
    }
}
