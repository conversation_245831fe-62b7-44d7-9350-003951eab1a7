package com.vbs.capsAllocation.service.impl;

import com.vbs.capsAllocation.dto.CreateDropdownConfigurationDTO;
import com.vbs.capsAllocation.dto.DropdownConfigurationDTO;
import com.vbs.capsAllocation.dto.UpdateDropdownConfigurationDTO;
import com.vbs.capsAllocation.model.DropdownConfiguration;
import com.vbs.capsAllocation.model.User;
import com.vbs.capsAllocation.repository.DropdownConfigurationRepository;
import com.vbs.capsAllocation.repository.UserRepository;
import com.vbs.capsAllocation.service.DropdownConfigurationService;
import com.vbs.capsAllocation.util.LoggerUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.server.ResponseStatusException;

import java.util.List;
import java.util.stream.Collectors;

@Service
@Transactional
public class DropdownConfigurationServiceImpl implements DropdownConfigurationService {
    
    @Autowired
    private DropdownConfigurationRepository dropdownConfigurationRepository;
    
    @Autowired
    private UserRepository userRepository;
    
    @Override
    public DropdownConfigurationDTO createDropdownOption(CreateDropdownConfigurationDTO createDTO, String createdBy) {
        LoggerUtil.logDebug("Creating dropdown option: {} for type: {}", createDTO.getOptionValue(), createDTO.getDropdownType());
        
        // Check if option already exists
        if (dropdownConfigurationRepository.existsByDropdownTypeAndOptionValue(createDTO.getDropdownType(), createDTO.getOptionValue())) {
            throw new ResponseStatusException(HttpStatus.CONFLICT, "Dropdown option already exists for this type");
        }
        
        // Get the user who is creating this option
        User user = userRepository.findByUsername(createdBy)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "User not found"));
        
        // Set sort order if not provided
        if (createDTO.getSortOrder() == null || createDTO.getSortOrder() == 0) {
            Integer maxSortOrder = dropdownConfigurationRepository.findMaxSortOrderByDropdownType(createDTO.getDropdownType());
            createDTO.setSortOrder(maxSortOrder + 1);
        }
        
        DropdownConfiguration dropdownConfig = new DropdownConfiguration();
        dropdownConfig.setDropdownType(createDTO.getDropdownType().toUpperCase());
        dropdownConfig.setOptionValue(createDTO.getOptionValue());
        dropdownConfig.setDisplayName(createDTO.getDisplayName());
        dropdownConfig.setIsActive(createDTO.getIsActive());
        dropdownConfig.setSortOrder(createDTO.getSortOrder());
        dropdownConfig.setCreatedBy(user);
        
        DropdownConfiguration savedConfig = dropdownConfigurationRepository.save(dropdownConfig);
        LoggerUtil.logDebug("Dropdown option created successfully with ID: {}", savedConfig.getId());
        
        return convertToDTO(savedConfig);
    }
    
    @Override
    @Transactional(readOnly = true)
    public List<DropdownConfigurationDTO> getActiveDropdownOptions(String dropdownType) {
        LoggerUtil.logDebug("Fetching active dropdown options for type: {}", dropdownType);
        
        List<DropdownConfiguration> configurations = dropdownConfigurationRepository
                .findActiveByDropdownTypeOrderBySortOrder(dropdownType.toUpperCase());
        
        return configurations.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }
    
    @Override
    @Transactional(readOnly = true)
    public List<DropdownConfigurationDTO> getAllDropdownOptions(String dropdownType) {
        LoggerUtil.logDebug("Fetching all dropdown options for type: {}", dropdownType);
        
        List<DropdownConfiguration> configurations = dropdownConfigurationRepository
                .findByDropdownTypeOrderBySortOrder(dropdownType.toUpperCase());
        
        return configurations.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }
    
    @Override
    public DropdownConfigurationDTO updateDropdownOption(Long id, UpdateDropdownConfigurationDTO updateDTO) {
        LoggerUtil.logDebug("Updating dropdown option with ID: {}", id);
        
        DropdownConfiguration existingConfig = dropdownConfigurationRepository.findById(id)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "Dropdown option not found"));
        
        existingConfig.setDisplayName(updateDTO.getDisplayName());
        existingConfig.setIsActive(updateDTO.getIsActive());
        
        if (updateDTO.getSortOrder() != null) {
            existingConfig.setSortOrder(updateDTO.getSortOrder());
        }
        
        DropdownConfiguration updatedConfig = dropdownConfigurationRepository.save(existingConfig);
        LoggerUtil.logDebug("Dropdown option updated successfully");
        
        return convertToDTO(updatedConfig);
    }
    
    @Override
    public void deleteDropdownOption(Long id) {
        LoggerUtil.logDebug("Deleting dropdown option with ID: {}", id);
        
        DropdownConfiguration config = dropdownConfigurationRepository.findById(id)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "Dropdown option not found"));
        
        dropdownConfigurationRepository.delete(config);
        LoggerUtil.logDebug("Dropdown option deleted successfully");
    }
    
    @Override
    @Transactional(readOnly = true)
    public DropdownConfigurationDTO getDropdownOptionById(Long id) {
        LoggerUtil.logDebug("Fetching dropdown option by ID: {}", id);
        
        DropdownConfiguration config = dropdownConfigurationRepository.findById(id)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "Dropdown option not found"));
        
        return convertToDTO(config);
    }
    
    @Override
    @Transactional(readOnly = true)
    public List<String> getAllDropdownTypes() {
        LoggerUtil.logDebug("Fetching all dropdown types");
        return dropdownConfigurationRepository.findDistinctDropdownTypes();
    }
    
    @Override
    public List<DropdownConfigurationDTO> reorderDropdownOptions(String dropdownType, List<Long> orderedIds) {
        LoggerUtil.logDebug("Reordering dropdown options for type: {}", dropdownType);
        
        for (int i = 0; i < orderedIds.size(); i++) {
            Long id = orderedIds.get(i);
            DropdownConfiguration config = dropdownConfigurationRepository.findById(id)
                    .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "Dropdown option not found: " + id));
            
            config.setSortOrder(i + 1);
            dropdownConfigurationRepository.save(config);
        }
        
        return getAllDropdownOptions(dropdownType);
    }
    
    private DropdownConfigurationDTO convertToDTO(DropdownConfiguration config) {
        DropdownConfigurationDTO dto = new DropdownConfigurationDTO();
        dto.setId(config.getId());
        dto.setDropdownType(config.getDropdownType());
        dto.setOptionValue(config.getOptionValue());
        dto.setDisplayName(config.getDisplayName());
        dto.setIsActive(config.getIsActive());
        dto.setSortOrder(config.getSortOrder());
        dto.setCreatedBy(config.getCreatedBy().getUsername());
        dto.setCreatedAt(config.getCreatedAt());
        dto.setUpdatedAt(config.getUpdatedAt());
        return dto;
    }
}
