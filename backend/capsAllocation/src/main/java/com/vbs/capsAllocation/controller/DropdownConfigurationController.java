package com.vbs.capsAllocation.controller;

import com.vbs.capsAllocation.dto.BaseResponse;
import com.vbs.capsAllocation.dto.CreateDropdownConfigurationDTO;
import com.vbs.capsAllocation.dto.DropdownConfigurationDTO;
import com.vbs.capsAllocation.dto.UpdateDropdownConfigurationDTO;
import com.vbs.capsAllocation.service.DropdownConfigurationService;
import com.vbs.capsAllocation.util.LoggerUtil;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/dropdown-configurations")
@CrossOrigin(origins = "*")
public class DropdownConfigurationController {
    
    @Autowired
    private DropdownConfigurationService dropdownConfigurationService;
    
    /**
     * Create a new dropdown configuration option (Admin/Ops Manager only)
     */
    @PreAuthorize("hasRole('ADMIN_OPS_MANAGER')")
    @PostMapping
    public ResponseEntity<BaseResponse<DropdownConfigurationDTO>> createDropdownOption(
            @AuthenticationPrincipal UserDetails userDetails,
            @Valid @RequestBody CreateDropdownConfigurationDTO createDTO) {
        try {
            LoggerUtil.logDebug("Creating dropdown option: {} by user: {}", createDTO.getOptionValue(), userDetails.getUsername());
            DropdownConfigurationDTO result = dropdownConfigurationService.createDropdownOption(createDTO, userDetails.getUsername());
            return ResponseEntity.status(HttpStatus.CREATED)
                    .body(BaseResponse.success("Dropdown option created successfully", result, HttpStatus.CREATED.value()));
        } catch (Exception e) {
            LoggerUtil.logError("Error creating dropdown option: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(BaseResponse.error("Failed to create dropdown option: " + e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR.value()));
        }
    }
    
    /**
     * Get all active dropdown options for a specific type (All authenticated users)
     */
    @PreAuthorize("hasRole('USER') or hasRole('LEAD') or hasRole('MANAGER') or hasRole('ACCOUNT_MANAGER') or hasRole('ADMIN_OPS_MANAGER')")
    @GetMapping("/active/{dropdownType}")
    public ResponseEntity<BaseResponse<List<DropdownConfigurationDTO>>> getActiveDropdownOptions(
            @PathVariable String dropdownType) {
        try {
            LoggerUtil.logDebug("Fetching active dropdown options for type: {}", dropdownType);
            List<DropdownConfigurationDTO> options = dropdownConfigurationService.getActiveDropdownOptions(dropdownType);
            return ResponseEntity.ok(BaseResponse.success("Active dropdown options retrieved successfully", options));
        } catch (Exception e) {
            LoggerUtil.logError("Error fetching active dropdown options: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(BaseResponse.error("Failed to retrieve dropdown options: " + e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR.value()));
        }
    }
    
    /**
     * Get all dropdown options for a specific type including inactive (Admin/Ops Manager only)
     */
    @PreAuthorize("hasRole('ADMIN_OPS_MANAGER')")
    @GetMapping("/all/{dropdownType}")
    public ResponseEntity<BaseResponse<List<DropdownConfigurationDTO>>> getAllDropdownOptions(
            @PathVariable String dropdownType) {
        try {
            LoggerUtil.logDebug("Fetching all dropdown options for type: {}", dropdownType);
            List<DropdownConfigurationDTO> options = dropdownConfigurationService.getAllDropdownOptions(dropdownType);
            return ResponseEntity.ok(BaseResponse.success("All dropdown options retrieved successfully", options));
        } catch (Exception e) {
            LoggerUtil.logError("Error fetching all dropdown options: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(BaseResponse.error("Failed to retrieve dropdown options: " + e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR.value()));
        }
    }
    
    /**
     * Update a dropdown configuration option (Admin/Ops Manager only)
     */
    @PreAuthorize("hasRole('ADMIN_OPS_MANAGER')")
    @PutMapping("/{id}")
    public ResponseEntity<BaseResponse<DropdownConfigurationDTO>> updateDropdownOption(
            @PathVariable Long id,
            @Valid @RequestBody UpdateDropdownConfigurationDTO updateDTO) {
        try {
            LoggerUtil.logDebug("Updating dropdown option with ID: {}", id);
            DropdownConfigurationDTO result = dropdownConfigurationService.updateDropdownOption(id, updateDTO);
            return ResponseEntity.ok(BaseResponse.success("Dropdown option updated successfully", result));
        } catch (Exception e) {
            LoggerUtil.logError("Error updating dropdown option: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(BaseResponse.error("Failed to update dropdown option: " + e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR.value()));
        }
    }
    
    /**
     * Delete a dropdown configuration option (Admin/Ops Manager only)
     */
    @PreAuthorize("hasRole('ADMIN_OPS_MANAGER')")
    @DeleteMapping("/{id}")
    public ResponseEntity<BaseResponse<Void>> deleteDropdownOption(@PathVariable Long id) {
        try {
            LoggerUtil.logDebug("Deleting dropdown option with ID: {}", id);
            dropdownConfigurationService.deleteDropdownOption(id);
            return ResponseEntity.ok(BaseResponse.success("Dropdown option deleted successfully", null));
        } catch (Exception e) {
            LoggerUtil.logError("Error deleting dropdown option: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(BaseResponse.error("Failed to delete dropdown option: " + e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR.value()));
        }
    }
    
    /**
     * Get a specific dropdown option by ID (Admin/Ops Manager only)
     */
    @PreAuthorize("hasRole('ADMIN_OPS_MANAGER')")
    @GetMapping("/{id}")
    public ResponseEntity<BaseResponse<DropdownConfigurationDTO>> getDropdownOptionById(@PathVariable Long id) {
        try {
            LoggerUtil.logDebug("Fetching dropdown option by ID: {}", id);
            DropdownConfigurationDTO option = dropdownConfigurationService.getDropdownOptionById(id);
            return ResponseEntity.ok(BaseResponse.success("Dropdown option retrieved successfully", option));
        } catch (Exception e) {
            LoggerUtil.logError("Error fetching dropdown option by ID: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(BaseResponse.error("Failed to retrieve dropdown option: " + e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR.value()));
        }
    }
    
    /**
     * Get all dropdown types (Admin/Ops Manager only)
     */
    @PreAuthorize("hasRole('ADMIN_OPS_MANAGER')")
    @GetMapping("/types")
    public ResponseEntity<BaseResponse<List<String>>> getAllDropdownTypes() {
        try {
            LoggerUtil.logDebug("Fetching all dropdown types");
            List<String> types = dropdownConfigurationService.getAllDropdownTypes();
            return ResponseEntity.ok(BaseResponse.success("Dropdown types retrieved successfully", types));
        } catch (Exception e) {
            LoggerUtil.logError("Error fetching dropdown types: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(BaseResponse.error("Failed to retrieve dropdown types: " + e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR.value()));
        }
    }
    
    /**
     * Reorder dropdown options for a specific type (Admin/Ops Manager only)
     */
    @PreAuthorize("hasRole('ADMIN_OPS_MANAGER')")
    @PutMapping("/reorder/{dropdownType}")
    public ResponseEntity<BaseResponse<List<DropdownConfigurationDTO>>> reorderDropdownOptions(
            @PathVariable String dropdownType,
            @RequestBody List<Long> orderedIds) {
        try {
            LoggerUtil.logDebug("Reordering dropdown options for type: {}", dropdownType);
            List<DropdownConfigurationDTO> result = dropdownConfigurationService.reorderDropdownOptions(dropdownType, orderedIds);
            return ResponseEntity.ok(BaseResponse.success("Dropdown options reordered successfully", result));
        } catch (Exception e) {
            LoggerUtil.logError("Error reordering dropdown options: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(BaseResponse.error("Failed to reorder dropdown options: " + e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR.value()));
        }
    }
}
