package com.vbs.capsAllocation.model;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.time.LocalDateTime;

@Entity
@Table(name = "time_entries")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class TimeEntry {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @ManyToOne
    @JoinColumn(name = "user_id", nullable = false)
    private User user;

    @ManyToOne
    @JoinColumn(name = "project_id", nullable = false)
    private Project project;

    @Column(nullable = false)
    private LocalDate entryDate;

    @Column(nullable = false)
    private String ldap;

    @ManyToOne
    @JoinColumn(name = "lead_id")
    private User lead;

    @Column(nullable = false)
    private String process;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private Activity activity;

    @Column(name = "time_in_mins", nullable = false)
    private Integer timeInMins;

    @Column(name = "attendance_type")
    private String attendanceType;

    private String comment;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private TimeEntryStatus status = TimeEntryStatus.PENDING;

    private String rejectionComment;

    @Column(name = "is_overtime")
    private Boolean isOvertime;

    // New fields for the updated structure

    @Column(name = "user_id_field")
    private String userIdField;

    @Column(name = "reporting_senior_user_id")
    private String reportingSeniorUserId;

    @Column(name = "resource_name")
    private String resourceName;

    @Column(name = "company")
    private String company;

    @Column(name = "entry_type")
    private String entryType;

    @Column(name = "notes", columnDefinition = "TEXT")
    private String notes;

    @Column(name = "internal_notes", columnDefinition = "TEXT")
    private String internalNotes;

    @Column(name = "date_worked")
    private LocalDate dateWorked;

    @Column(name = "hours_worked")
    private Double hoursWorked;

    @Column(name = "billable_hours_worked")
    private Double billableHoursWorked;

    @Column(name = "created_at")
    private LocalDateTime createdAt;

    @Column(name = "updated_at")
    private LocalDateTime updatedAt;
}
