package com.vbs.capsAllocation.service;

import com.vbs.capsAllocation.dto.CreateDropdownConfigurationDTO;
import com.vbs.capsAllocation.dto.DropdownConfigurationDTO;
import com.vbs.capsAllocation.dto.UpdateDropdownConfigurationDTO;

import java.util.List;

/**
 * Service interface for managing dropdown configurations.
 */
public interface DropdownConfigurationService {
    
    /**
     * Create a new dropdown configuration option.
     */
    DropdownConfigurationDTO createDropdownOption(CreateDropdownConfigurationDTO createDTO, String createdBy);
    
    /**
     * Get all active dropdown options for a specific dropdown type.
     */
    List<DropdownConfigurationDTO> getActiveDropdownOptions(String dropdownType);
    
    /**
     * Get all dropdown options for a specific dropdown type (including inactive).
     */
    List<DropdownConfigurationDTO> getAllDropdownOptions(String dropdownType);
    
    /**
     * Update an existing dropdown configuration option.
     */
    DropdownConfigurationDTO updateDropdownOption(Long id, UpdateDropdownConfigurationDTO updateDTO);
    
    /**
     * Delete a dropdown configuration option.
     */
    void deleteDropdownOption(Long id);
    
    /**
     * Get a dropdown configuration option by ID.
     */
    DropdownConfigurationDTO getDropdownOptionById(Long id);
    
    /**
     * Get all distinct dropdown types.
     */
    List<String> getAllDropdownTypes();
    
    /**
     * Reorder dropdown options for a specific type.
     */
    List<DropdownConfigurationDTO> reorderDropdownOptions(String dropdownType, List<Long> orderedIds);
}
