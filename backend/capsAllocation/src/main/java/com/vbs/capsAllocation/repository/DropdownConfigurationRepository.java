package com.vbs.capsAllocation.repository;

import com.vbs.capsAllocation.model.DropdownConfiguration;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface DropdownConfigurationRepository extends JpaRepository<DropdownConfiguration, Long> {
    
    /**
     * Find all active dropdown options for a specific dropdown type, ordered by sort_order
     */
    @Query("SELECT dc FROM DropdownConfiguration dc WHERE dc.dropdownType = :dropdownType AND dc.isActive = true ORDER BY dc.sortOrder ASC, dc.displayName ASC")
    List<DropdownConfiguration> findActiveByDropdownTypeOrderBySortOrder(@Param("dropdownType") String dropdownType);
    
    /**
     * Find all dropdown options for a specific dropdown type (including inactive), ordered by sort_order
     */
    @Query("SELECT dc FROM DropdownConfiguration dc WHERE dc.dropdownType = :dropdownType ORDER BY dc.sortOrder ASC, dc.displayName ASC")
    List<DropdownConfiguration> findByDropdownTypeOrderBySortOrder(@Param("dropdownType") String dropdownType);
    
    /**
     * Find a specific dropdown option by type and value
     */
    Optional<DropdownConfiguration> findByDropdownTypeAndOptionValue(String dropdownType, String optionValue);
    
    /**
     * Check if a dropdown option exists for a specific type and value
     */
    boolean existsByDropdownTypeAndOptionValue(String dropdownType, String optionValue);
    
    /**
     * Get all distinct dropdown types
     */
    @Query("SELECT DISTINCT dc.dropdownType FROM DropdownConfiguration dc ORDER BY dc.dropdownType")
    List<String> findDistinctDropdownTypes();
    
    /**
     * Get the maximum sort order for a specific dropdown type
     */
    @Query("SELECT COALESCE(MAX(dc.sortOrder), 0) FROM DropdownConfiguration dc WHERE dc.dropdownType = :dropdownType")
    Integer findMaxSortOrderByDropdownType(@Param("dropdownType") String dropdownType);
    
    /**
     * Find dropdown configurations by dropdown type and active status
     */
    List<DropdownConfiguration> findByDropdownTypeAndIsActive(String dropdownType, Boolean isActive);
}
