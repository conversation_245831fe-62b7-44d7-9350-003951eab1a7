<form [formGroup]="form" (ngSubmit)="submit()">
  <div>
    <label>User ID</label>
    <input type="text" [value]="userId" readonly />
  </div>
  <div>
    <label>Reporting Senior User ID</label>
    <input type="text" [value]="reportingSeniorUserId" readonly />
  </div>
  <div>
    <label>Resource Name</label>
    <input type="text" [value]="resourceName" readonly />
  </div>
  <div>
    <label>Company</label>
    <input type="text" [value]="company" readonly />
  </div>
  <div>
    <label>Type</label>
    <input type="text" value="Ticket" readonly />
  </div>
  <div>
    <label>Date Worked</label>
    <input type="date" formControlName="dateWorked" required />
  </div>
  <div>
    <label>Hours Worked</label>
    <input type="number" formControlName="hoursWorked" min="0.01" step="0.01" required />
  </div>
  <div>
    <label>Billable Hours Worked</label>
    <input type="number" formControlName="billableHoursWorked" min="0" step="0.01" required />
  </div>
  <div>
    <label>Notes</label>
    <textarea formControlName="notes"></textarea>
  </div>
  <div>
    <label>Internal Notes</label>
    <textarea formControlName="internalNotes"></textarea>
  </div>
  <div>
    <button type="submit" [disabled]="form.invalid || submitting">Submit</button>
  </div>
  <div *ngIf="successMsg" class="success">{{ successMsg }}</div>
  <div *ngIf="errorMsg" class="error">{{ errorMsg }}</div>
</form> 