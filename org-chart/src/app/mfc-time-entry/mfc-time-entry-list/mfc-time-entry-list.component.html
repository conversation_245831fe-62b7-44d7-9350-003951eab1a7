<div *ngIf="loading">Loading...</div>
<div *ngIf="errorMsg" class="error">{{ errorMsg }}</div>
<table *ngIf="entries.length">
  <thead>
    <tr>
      <th>Date Worked</th>
      <th>Hours</th>
      <th>Billable</th>
      <th>Status</th>
      <th>Notes</th>
      <th>Actions</th>
    </tr>
  </thead>
  <tbody>
    <tr *ngFor="let entry of entries">
      <td>{{ entry.dateWorked }}</td>
      <td>{{ entry.hoursWorked }}</td>
      <td>{{ entry.billableHoursWorked }}</td>
      <td>{{ entry.status }}</td>
      <td>{{ entry.notes }}</td>
      <td>
        <!-- Edit and Delete actions (edit can be a routerLink to edit form) -->
        <button (click)="deleteEntry(entry.id!)">Delete</button>
      </td>
    </tr>
  </tbody>
</table>
<div *ngIf="!loading && !entries.length">No entries found.</div> 