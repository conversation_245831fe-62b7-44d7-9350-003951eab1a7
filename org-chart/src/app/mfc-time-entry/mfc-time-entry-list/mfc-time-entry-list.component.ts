import { Component, OnInit } from '@angular/core';
import { MfcTimeEntryService } from '../../services/mfc-time-entry.service';
import { MfcTimeEntry } from '../../model/mfc-time-entry.model';
import { AuthService } from 'src/app/auth.service';

@Component({
  selector: 'app-mfc-time-entry-list',
  templateUrl: './mfc-time-entry-list.component.html',
  styleUrls: ['./mfc-time-entry-list.component.css']
})
export class MfcTimeEntryListComponent implements OnInit {
  entries: MfcTimeEntry[] = [];
  loading = false;
  errorMsg = '';
  userId = '';

  constructor(
    private mfcService: MfcTimeEntryService,
    private auth: AuthService
  ) {}

  ngOnInit() {
    this.userId = localStorage.getItem('username') || '';
    this.loadEntries();
  }

  loadEntries() {
    this.loading = true;
    this.mfcService.getMyEntries().subscribe({
      next: data => {
        this.entries = data;
        this.loading = false;
      },
      error: err => {
        this.errorMsg = err.error?.message || 'Failed to load entries.';
        this.loading = false;
      }
    });
  }

  deleteEntry(id: number) {
    if (!confirm('Delete this entry?')) return;
    this.mfcService.delete(id).subscribe({
      next: () => this.loadEntries(),
      error: err => alert(err.error?.message || 'Delete failed.')
    });
  }
} 