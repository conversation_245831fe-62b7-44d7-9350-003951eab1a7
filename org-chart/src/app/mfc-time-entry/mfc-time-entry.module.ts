import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormsModule } from '@angular/forms';
import { MfcTimeEntryFormComponent } from './mfc-time-entry-form/mfc-time-entry-form.component';
import { MfcTimeEntryListComponent } from './mfc-time-entry-list/mfc-time-entry-list.component';

@NgModule({
  declarations: [
    MfcTimeEntryFormComponent,
    MfcTimeEntryListComponent
  ],
  imports: [
    CommonModule,
    ReactiveFormsModule,
    FormsModule
  ],
  exports: [
    MfcTimeEntryFormComponent,
    MfcTimeEntryListComponent
  ]
})
export class MfcTimeEntryModule {} 