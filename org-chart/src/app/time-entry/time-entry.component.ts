import { <PERSON><PERSON>iew<PERSON>nit, Component, OnInit, ViewChild, <PERSON><PERSON><PERSON>roy, ChangeDetectorRef } from '@angular/core';
import { Router } from '@angular/router';
import { MatTableDataSource } from '@angular/material/table';
import { MatPaginator } from '@angular/material/paginator';
import { MatSort } from '@angular/material/sort';
import { MatDialog } from '@angular/material/dialog';
import { NotificationService } from '../shared/notification.service';
import { TimeEntryFormComponent } from './time-entry-form/time-entry-form.component';
import { ConfirmationDialogComponent } from '../confirm-dialog/confirmation-dialog.component';
import { WeekCopyDialogComponent } from './week-copy-dialog/week-copy-dialog.component';
import { HttpClient } from '@angular/common/http';
import { FormControl, FormGroup } from '@angular/forms';
import { environment } from '../../environments/environment';
import { MatMenuTrigger } from '@angular/material/menu';
import { saveAs } from 'file-saver';
import * as XLSX from 'xlsx';

// Interface for the backend BaseResponse structure
interface BaseResponse<T> {
  status: string;
  code: number;
  message: string;
  data: T;
}

// Updated interface to match the new field structure
export interface TimeEntry {
  id: number;
  userId: number;
  username?: string;
  userName?: string; // For backward compatibility
  projectId: number;
  projectCode?: string;
  projectName: string;

  // New field structure
  userIdField?: string; // User ID (replaces ldap)
  reportingSeniorUserId?: string; // Reporting Senior's UserID
  resourceName?: string; // Resource Name (lastname, firstname)
  company?: string; // Company
  type?: string; // Type (replaces activity)
  notes?: string; // Notes (replaces comment)
  internalNotes?: string; // Internal Notes
  dateWorked?: string; // Date Worked (replaces entryDate)
  hoursWorked?: number; // Hours Worked (replaces timeInMins converted)
  billableHoursWorked?: number; // Billable Hours Worked

  // Legacy fields for backward compatibility
  entryDate?: string; // From API
  date?: string;      // For UI display
  timeInMins?: number; // From API
  hours?: number;      // For UI display
  activity?: string;   // From API
  description?: string; // For UI display
  status: string;
  comment?: string;    // From API
  comments?: string;   // For UI display
  rejectionComment?: string;
  createdAt?: string;
  updatedAt?: string;
  ldap?: string; // Legacy field
  leadId?: number;
  leadUsername?: string;
  process?: string;
  attendanceType?: string;
  isOvertime?: boolean; // Flag to indicate if this is an overtime entry
}

@Component({
  selector: 'app-time-entry',
  templateUrl: './time-entry.component.html',
  styleUrls: ['./time-entry.component.css']
})
export class TimeEntryComponent implements OnInit, AfterViewInit, OnDestroy {
  // Store the resize handler for cleanup
  private resizeHandler!: () => void;
  baseUrl = environment.apiUrl;
  dataSource = new MatTableDataSource<TimeEntry>([]);
  displayedColumns: string[] = ['dateWorked', 'userIdField', 'reportingSeniorUserId', 'resourceName', 'company', 'type', 'hoursWorked', 'billableHoursWorked', 'notes', 'status', 'actions'];
  allColumns: string[] = ['dateWorked', 'userIdField', 'reportingSeniorUserId', 'resourceName', 'company', 'type', 'notes', 'internalNotes', 'hoursWorked', 'billableHoursWorked', 'status', 'actions'];
  totalRecords = 0;
  showFilters = false;
  showColumnFilters = false;
  filterValues: any = {};

  // Column toggle properties
  columnDisplayNames: { [key: string]: string } = {
    'dateWorked': 'Date Worked',
    'userIdField': 'User ID',
    'reportingSeniorUserId': 'Reporting Senior\'s UserID',
    'resourceName': 'Resource Name',
    'company': 'Company',
    'type': 'Type',
    'notes': 'Notes',
    'internalNotes': 'Internal Notes',
    'hoursWorked': 'Hours Worked',
    'billableHoursWorked': 'Billable Hours Worked',
    'status': 'Status',
    'actions': 'Actions'
  };

  // Column toggle search
  columnSearchText: string = '';
  allColumnsSelected: boolean = false;
  dateRange = new FormGroup({
    start: new FormControl<Date | null>(this.getLastWeekStartDate()),
    end: new FormControl<Date | null>(new Date())
  });

  userRole: string | undefined;

  // Column filtering properties
  columnUniqueValues: { [key: string]: string[] } = {};
  currentFilterMenuState = {
    columnKey: null as string | null,
    tempSelectedValues: [] as string[],
    searchText: ''
  };
  currentTrigger: MatMenuTrigger | null = null;

  @ViewChild(MatPaginator) paginator!: MatPaginator;
  @ViewChild(MatSort) sort!: MatSort;

  constructor(
    private http: HttpClient,
    private router: Router,
    private dialog: MatDialog,
    private notificationService: NotificationService,
    private cdr: ChangeDetectorRef
  ) {}

  ngOnInit(): void {
    this.userRole = localStorage.getItem('role') || undefined;

    // Load displayed columns first to avoid change detection issues
    this.loadDisplayedColumns();

    // Set up filter predicate for both global and column filters
    this.dataSource.filterPredicate = (data: TimeEntry, filter: string) => {
      // For global filter (simple string)
      if (!filter.includes('{')) {
        return Object.values(data)
          .some(value => value?.toString().toLowerCase().includes(filter.toLowerCase()));
      }

      // For column filters (JSON object)
      try {
        const filterObject = JSON.parse(filter);
        const keys = Object.keys(filterObject);

        if (keys.length === 0) return true;

        return keys.every(key => {
          const filterValues = filterObject[key];

          // Skip empty filters
          if (!filterValues || (Array.isArray(filterValues) && filterValues.length === 0)) {
            return true;
          }

          // Handle array of filter values (for column filter menu)
          if (Array.isArray(filterValues)) {
            const dataValue = String(data[key as keyof TimeEntry] || '').toLowerCase();
            return filterValues.some(value => dataValue.includes(value.toLowerCase()));
          }

          // Handle string filter values (for text input filters)
          const dataValue = String(data[key as keyof TimeEntry] || '').toLowerCase();
          return dataValue.includes(filterValues.toLowerCase());
        });
      } catch (error) {
        console.error('Error parsing filter:', error);
        return true;
      }
    };

    this.loadTimeEntries();
  }

  ngAfterViewInit(): void {
    this.dataSource.paginator = this.paginator;
    this.dataSource.sort = this.sort;

    // Add scroll detection for the table
    this.detectTableScroll();

    // Create and store the resize handler
    this.resizeHandler = () => {
      this.detectTableScroll();
    };

    // Listen for window resize to update scroll detection
    window.addEventListener('resize', this.resizeHandler);
  }

  /**
   * Detects if the table is scrollable horizontally and adds a class accordingly
   */
  detectTableScroll(): void {
    setTimeout(() => {
      const tableContainer = document.querySelector('.table-responsive') as HTMLElement;
      if (tableContainer) {
        // Check if the content width is greater than the container width
        // Add a small buffer (20px) to ensure action buttons aren't cut off
        const isScrollable = tableContainer.scrollWidth > (tableContainer.clientWidth - 20);

        // Add or remove the 'scrollable' class based on whether the table is scrollable
        if (isScrollable) {
          tableContainer.classList.add('scrollable');
        } else {
          tableContainer.classList.remove('scrollable');
        }

        // Ensure the action buttons are visible by checking if they're in view
        const actionCells = document.querySelectorAll('.actions-cell') as NodeListOf<HTMLElement>;
        if (actionCells && actionCells.length > 0) {
          // Check if any action cell is partially out of view
          Array.from(actionCells).forEach(cell => {
            const cellRect = cell.getBoundingClientRect();
            const containerRect = tableContainer.getBoundingClientRect();

            // If the cell is partially out of view, ensure the table is scrollable
            if (cellRect.right > containerRect.right) {
              tableContainer.classList.add('scrollable');
            }
          });
        }
      }
    }, 500); // Delay to ensure the table has rendered
  }

  /**
   * Cleanup resources when component is destroyed
   */
  ngOnDestroy(): void {
    // Remove the resize event listener
    if (this.resizeHandler) {
      window.removeEventListener('resize', this.resizeHandler);
    }
  }

  getLastWeekStartDate(): Date {
    const date = new Date();
    date.setDate(date.getDate() - 7);
    return date;
  }

  loadTimeEntries(): void {
    const startDate = this.formatDate(this.dateRange.get('start')?.value || this.getLastWeekStartDate());
    const endDate = this.formatDate(this.dateRange.get('end')?.value || new Date());

    this.http.get<BaseResponse<TimeEntry[]>>(`${this.baseUrl}/api/time-entries?startDate=${startDate}&endDate=${endDate}`)
      .subscribe({
        next: (response) => {
          console.log('Received time entries response:', response);
          if (response.status === 'success') {
            const mappedData = response.data.map(entry => this.mapTimeEntry(entry));
            this.dataSource.data = mappedData;
            this.totalRecords = mappedData.length;

            // Collect unique values for column filters
            this.collectUniqueColumnValues(mappedData);

            // Update scroll detection after data loads
            setTimeout(() => {
              this.detectTableScroll();
            }, 100);
          } else {
            // Handle error response from backend
            this.notificationService.showNotification({
              type: 'error',
              message: response.message || 'Failed to load time entries'
            });
          }
        },
        error: (error) => {
          console.error('Error fetching time entries:', error);
          // Extract error message and let the service transform it
          const errorMessage = error.error?.message || 'Failed to load time entries. Please try again.';
          this.notificationService.showError(errorMessage);

          if (error.status === 0) {
            this.addSampleData();
          }
        }
      });
  }

  // Collect unique values for each column to use in filters
  collectUniqueColumnValues(data: TimeEntry[]): void {
    // Define columns that should have filters
    const columnsToFilter = ['date', 'ldap', 'leadUsername', 'projectName', 'process',
                            'activity', 'timeInMins', 'attendanceType', 'comment', 'status'];

    // Reset the unique values
    this.columnUniqueValues = {};

    // For each column, collect unique values
    columnsToFilter.forEach(column => {
      const uniqueValues = new Set<string>();

      data.forEach(entry => {
        const value = entry[column as keyof TimeEntry];
        if (value !== undefined && value !== null) {
          uniqueValues.add(String(value));
        }
      });

      this.columnUniqueValues[column] = Array.from(uniqueValues).sort();
    });
  }

  // Map API response to UI format
  mapTimeEntry(entry: TimeEntry): TimeEntry {
    // Extract User ID from email if needed (<NAME_EMAIL>)
    const extractUserIdFromEmail = (email: string): string => {
      if (email && email.includes('@')) {
        return email.split('@')[0];
      }
      return email;
    };

    return {
      ...entry,
      // New field mappings
      dateWorked: entry.dateWorked || entry.entryDate || entry.date,
      userIdField: entry.userIdField || extractUserIdFromEmail(entry.ldap || entry.username || ''),
      reportingSeniorUserId: entry.reportingSeniorUserId || extractUserIdFromEmail(entry.leadUsername || ''),
      resourceName: entry.resourceName || `${entry.userName || ''}, ${entry.username || ''}`.replace(', ,', '').trim(),
      company: entry.company || 'ABC', // Default company as specified
      type: entry.type || entry.activity || 'Ticket', // Default type as specified
      notes: entry.notes || entry.comment || entry.comments || '',
      internalNotes: entry.internalNotes || '',
      hoursWorked: entry.hoursWorked || (entry.timeInMins ? entry.timeInMins / 60 : 0),
      billableHoursWorked: entry.billableHoursWorked || (entry.timeInMins ? entry.timeInMins / 60 : 0), // Default to same as hours worked

      // Legacy field mappings for backward compatibility
      date: entry.entryDate || entry.date,
      hours: entry.timeInMins ? entry.timeInMins / 60 : 0,
      description: entry.activity || entry.description,
      comments: entry.comment || entry.comments,
      userName: entry.username || entry.userName || entry.ldap,
      leadUsername: entry.leadUsername,
      process: entry.process,
      ldap: entry.ldap,
      comment: entry.comment,
      attendanceType: entry.attendanceType,
      isOvertime: entry.isOvertime || false, // Default to false if isOvertime is not provided
    };
  }

  // Add sample data for testing
  addSampleData(): void {
    const sampleData: TimeEntry[] = [
      {
        id: 1,
        userId: 1606,
        username: "akhilbhatnagar",
        projectId: 1,
        projectCode: "PROJ001",
        projectName: "Customer Portal Development",
        entryDate: "2025-04-02",
        ldap: "akhilbhatnagar",
        leadId: 1606,
        leadUsername: "akhilbhatnagar",
        process: "Development",
        activity: "DEVELOPMENT",
        timeInMins: 240,
        comment: "Working on user authentication module",
        status: "APPROVED",
        rejectionComment: undefined
      },
      {
        id: 3,
        userId: 1606,
        username: "akhilbhatnagar",
        projectId: 1,
        projectCode: "PROJ001",
        projectName: "Customer Portal Development",
        entryDate: "2025-04-02",
        ldap: "akhilbhatnagar",
        leadId: 1606,
        leadUsername: "akhilbhatnagar",
        process: "OPS EXCELLENCE",
        activity: "DEVELOPMENT",
        timeInMins: 100,
        comment: "Working on user authentication module",
        status: "PENDING",
        rejectionComment: undefined
      }
    ];

    // Map the sample data
    const mappedData = sampleData.map(entry => this.mapTimeEntry(entry));
    this.dataSource.data = mappedData;
    this.totalRecords = mappedData.length;

    // Collect unique values for column filters
    this.collectUniqueColumnValues(mappedData);
  }

  formatDate(date: Date): string {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  }

  applyDateFilter(): void {
    this.loadTimeEntries();
  }

  applyFilter(event: Event, column: string): void {
    const filterValue = (event.target as HTMLInputElement).value;
    this.filterValues[column] = filterValue.trim().toLowerCase();
    this.applyFilterValues();
  }

  applyFilterValues(): void {
    this.dataSource.filter = JSON.stringify(this.filterValues);
    if (this.dataSource.paginator) {
      this.dataSource.paginator.firstPage();
    }
  }

  applyGlobalFilter(event: Event): void {
    const filterValue = (event.target as HTMLInputElement).value.trim().toLowerCase();

    // Clear column filters when global search is used
    if (filterValue) {
      this.filterValues = {};

      // Set up global filter predicate
      this.dataSource.filterPredicate = (data: TimeEntry, filter: string): boolean => {
        const searchTerm = filter.toLowerCase();
        const dataStr = Object.keys(data as any).reduce((currentTerm, key) => {
          // Combine relevant fields for global search
          return currentTerm + (data as any)[key] + '◬'; // Use a unique separator
        }, '').toLowerCase();
        return dataStr.indexOf(searchTerm) !== -1;
      };
    } else {
      // Reset to column filter predicate when global filter is cleared
      this.dataSource.filterPredicate = (data: TimeEntry, filter: string) => {
        // For global filter (simple string)
        if (!filter.includes('{')) {
          return Object.values(data)
            .some(value => value?.toString().toLowerCase().includes(filter.toLowerCase()));
        }

        // For column filters (JSON object)
        try {
          const filterObject = JSON.parse(filter);
          const keys = Object.keys(filterObject);

          if (keys.length === 0) return true;

          return keys.every(key => {
            const filterValues = filterObject[key];

            // Skip empty filters
            if (!filterValues || (Array.isArray(filterValues) && filterValues.length === 0)) {
              return true;
            }

            // Handle array of filter values (for column filter menu)
            if (Array.isArray(filterValues)) {
              const dataValue = String(data[key as keyof TimeEntry] || '').toLowerCase();
              return filterValues.some(value => dataValue.includes(value.toLowerCase()));
            }

            // Handle string filter values (for text input filters)
            const dataValue = String(data[key as keyof TimeEntry] || '').toLowerCase();
            return dataValue.includes(filterValues.toLowerCase());
          });
        } catch (error) {
          console.error('Error parsing filter:', error);
          return true;
        }
      };
    }

    this.dataSource.filter = filterValue;
    if (this.dataSource.paginator) {
      this.dataSource.paginator.firstPage();
    }
  }

  toggleColumnFilters(): void {
    this.showColumnFilters = !this.showColumnFilters;
  }

  // Column filter menu methods
  openFilterMenu(columnKey: string, trigger: MatMenuTrigger): void {
    this.currentTrigger = trigger;
    this.currentFilterMenuState.columnKey = columnKey;
    this.currentFilterMenuState.tempSelectedValues =
      this.filterValues[columnKey] ? [...this.filterValues[columnKey]] : [];
    this.currentFilterMenuState.searchText = '';
  }

  resetCurrentFilterMenuState(): void {
    this.currentFilterMenuState.columnKey = null;
    this.currentFilterMenuState.tempSelectedValues = [];
    this.currentFilterMenuState.searchText = '';
    this.currentTrigger = null;
  }

  isTempSelected(value: string): boolean {
    return this.currentFilterMenuState.tempSelectedValues.includes(value);
  }

  toggleTempSelection(value: string, checked: boolean): void {
    if (checked) {
      if (!this.isTempSelected(value)) {
        this.currentFilterMenuState.tempSelectedValues.push(value);
      }
    } else {
      const index = this.currentFilterMenuState.tempSelectedValues.indexOf(value);
      if (index >= 0) {
        this.currentFilterMenuState.tempSelectedValues.splice(index, 1);
      }
    }
  }

  isAllTempSelected(): boolean {
    const filteredOptions = this.getFilteredMenuOptions();
    return filteredOptions.length > 0 &&
           filteredOptions.every(value => this.isTempSelected(value));
  }

  isSomeTempSelected(): boolean {
    const filteredOptions = this.getFilteredMenuOptions();
    return filteredOptions.some(value => this.isTempSelected(value)) &&
           !this.isAllTempSelected();
  }

  toggleSelectAllTemp(checked: boolean): void {
    const filteredOptions = this.getFilteredMenuOptions();

    if (checked) {
      // Add all filtered options that aren't already selected
      filteredOptions.forEach(value => {
        if (!this.isTempSelected(value)) {
          this.currentFilterMenuState.tempSelectedValues.push(value);
        }
      });
    } else {
      // Remove all filtered options
      this.currentFilterMenuState.tempSelectedValues =
        this.currentFilterMenuState.tempSelectedValues.filter(
          value => !filteredOptions.includes(value)
        );
    }
  }

  onFilterApplied(): void {
    if (this.currentFilterMenuState.columnKey) {
      const key = this.currentFilterMenuState.columnKey;
      this.filterValues[key] = [...this.currentFilterMenuState.tempSelectedValues];
      this.applyColumnFilters();
    }

    // Close the menu
    if (this.currentTrigger) {
      this.currentTrigger.closeMenu();
    }
  }

  clearColumnFilter(): void {
    if (this.currentFilterMenuState.columnKey) {
      const key = this.currentFilterMenuState.columnKey;
      this.filterValues[key] = [];
      this.currentFilterMenuState.tempSelectedValues = [];
      this.applyColumnFilters();

      // Keep the menu open after clearing
      const trigger = this.currentTrigger;
      if (trigger) {
        setTimeout(() => {
          trigger.openMenu();
        });
      }
    }
  }

  applyColumnFilters(): void {
    const activeFilters = Object.keys(this.filterValues).reduce((acc, key) => {
      if (this.filterValues[key]?.length > 0) {
        acc[key] = this.filterValues[key];
      }
      return acc;
    }, {} as { [key: string]: string[] });

    this.dataSource.filter = JSON.stringify(activeFilters);

    if (this.dataSource.paginator) {
      this.dataSource.paginator.firstPage();
    }
  }

  getUniqueColumnValues(columnKey: string): string[] {
    return this.columnUniqueValues[columnKey] || [];
  }

  get filteredMenuOptions(): string[] {
    return this.getFilteredMenuOptions();
  }

  getFilteredMenuOptions(): string[] {
    if (!this.currentFilterMenuState.columnKey) return [];

    const columnKey = this.currentFilterMenuState.columnKey;
    const searchText = this.currentFilterMenuState.searchText.toLowerCase();
    const options = this.getUniqueColumnValues(columnKey);

    if (!searchText) return options;

    return options.filter(option =>
      option.toLowerCase().includes(searchText)
    );
  }

  isFilterActive(columnKey: string): boolean {
    return this.filterValues[columnKey]?.length > 0;
  }

  // Column toggle methods
  getFilteredColumns(): string[] {
    if (!this.columnSearchText) {
      return this.allColumns;
    }

    return this.allColumns.filter(column =>
      this.columnDisplayNames[column].toLowerCase().includes(this.columnSearchText.toLowerCase())
    );
  }

  toggleAllColumns(checked: boolean): void {
    this.allColumnsSelected = checked;

    const filteredColumns = this.getFilteredColumns();

    if (checked) {
      // Add all filtered columns
      this.displayedColumns = [...this.allColumns];
    } else {
      // Remove all filtered columns (except actions which should always be present)
      this.displayedColumns = this.displayedColumns.filter(column =>
        !filteredColumns.includes(column) || column === 'actions'
      );
    }

    // Save the displayed columns
    this.saveDisplayedColumns();

    // Update scroll detection after columns change
    setTimeout(() => {
      this.detectTableScroll();
    }, 100);
  }

  toggleColumn(column: string): void {
    const index = this.displayedColumns.indexOf(column);

    if (index === -1) {
      // Add the column
      const allColumnsIndex = this.allColumns.indexOf(column);
      if (allColumnsIndex > -1) {
        // Find where to insert the column (maintain original order)
        let insertIndex = 0;
        for (let i = 0; i < allColumnsIndex; i++) {
          if (this.displayedColumns.includes(this.allColumns[i])) {
            insertIndex = this.displayedColumns.indexOf(this.allColumns[i]) + 1;
          }
        }
        this.displayedColumns.splice(insertIndex, 0, column);
      }
    } else {
      // Remove the column
      this.displayedColumns.splice(index, 1);
    }

    // Update allColumnsSelected state
    this.updateAllColumnsSelectedState();

    // Save the displayed columns
    this.saveDisplayedColumns();

    // Update scroll detection after columns change
    setTimeout(() => {
      this.detectTableScroll();
    }, 100);
  }

  isColumnDisplayed(column: string): boolean {
    return this.displayedColumns.includes(column);
  }

  updateAllColumnsSelectedState(): void {
    // Use setTimeout to avoid ExpressionChangedAfterItHasBeenCheckedError
    setTimeout(() => {
      const filteredColumns = this.getFilteredColumns();
      const selectableFilteredColumns = filteredColumns.filter(col => col !== 'actions');

      const allFilteredColumnsSelected = selectableFilteredColumns.every(col =>
        this.displayedColumns.includes(col)
      );

      this.allColumnsSelected = allFilteredColumnsSelected;
      this.cdr.detectChanges();
    });
  }

  saveDisplayedColumns(): void {
    localStorage.setItem('timeEntryTableDisplayedColumns', JSON.stringify(this.displayedColumns));
  }

  loadDisplayedColumns(): void {
    const savedColumns = localStorage.getItem('timeEntryTableDisplayedColumns');
    if (savedColumns) {
      const parsedColumns = JSON.parse(savedColumns);

      // Check if saved columns contain old field names and migrate them
      const oldToNewFieldMap: { [key: string]: string } = {
        'date': 'dateWorked',
        'ldap': 'userIdField',
        'leadUsername': 'reportingSeniorUserId',
        'projectName': 'resourceName',
        'process': 'company',
        'activity': 'type',
        'timeInMins': 'hoursWorked',
        'attendanceType': 'billableHoursWorked',
        'comment': 'notes'
      };

      // Check if any old field names exist
      const hasOldFields = parsedColumns.some((col: string) => oldToNewFieldMap[col]);

      if (hasOldFields) {
        // Clear old localStorage and use default new columns
        localStorage.removeItem('timeEntryTableDisplayedColumns');
        this.displayedColumns = ['dateWorked', 'userIdField', 'reportingSeniorUserId', 'resourceName', 'company', 'type', 'hoursWorked', 'billableHoursWorked', 'notes', 'status', 'actions'];
      } else {
        this.displayedColumns = parsedColumns;

        // Ensure 'actions' column is always present
        if (!this.displayedColumns.includes('actions')) {
          this.displayedColumns.push('actions');
        }
      }
    } else {
      // Set default columns with new field names
      this.displayedColumns = ['dateWorked', 'userIdField', 'reportingSeniorUserId', 'resourceName', 'company', 'type', 'hoursWorked', 'billableHoursWorked', 'notes', 'status', 'actions'];
    }

    // Update allColumnsSelected state
    this.updateAllColumnsSelectedState();

    // Trigger change detection to prevent ExpressionChangedAfterItHasBeenCheckedError
    setTimeout(() => {
      this.cdr.detectChanges();
    });
  }

  openAddTimeEntryForm(): void {
    const dialogRef = this.dialog.open(TimeEntryFormComponent, {
      width: '800px',
      data: {
        isEditMode: false,
        existingEntries: this.dataSource.data  // Pass current time entries
      }
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.loadTimeEntries();
      }
    });
  }

  editTimeEntry(timeEntry: TimeEntry): void {
    const dialogRef = this.dialog.open(TimeEntryFormComponent, {
      width: '800px',
      data: {
        isEditMode: true,
        timeEntry: timeEntry,
        existingEntries: this.dataSource.data  // Pass current time entries
      }
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.loadTimeEntries();
      }
    });
  }

  deleteTimeEntry(timeEntry: TimeEntry): void {
    const dialogRef = this.dialog.open(ConfirmationDialogComponent, {
      width: '400px',
      data: { title: 'Confirm Delete', message: 'Are you sure you want to delete this time entry?' }
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.http.delete<BaseResponse<any>>(`${this.baseUrl}/api/time-entries/${timeEntry.id}`)
          .subscribe({
            next: (response) => {
              if (response.status === 'success') {
                this.notificationService.showNotification({
                  type: 'success',
                  message: response.message || 'Time entry deleted successfully!'
                });
                this.loadTimeEntries();
              } else {
                this.notificationService.showNotification({
                  type: 'error',
                  message: response.message || 'Failed to delete time entry'
                });
              }
            },
            error: (error) => {
              console.error('Error deleting time entry:', error);
              // Extract error message and let the service transform it
              const errorMessage = error.error?.message || 'Failed to delete time entry. Please try again.';
              this.notificationService.showError(errorMessage);
            }
          });
      }
    });
  }

  viewProjects(): void {
    this.router.navigate(['/time-entry/projects']);
  }

  viewRequests(): void {
    this.router.navigate(['/time-entry/requests']);
  }

  assignProjects(): void {
    this.router.navigate(['/time-entry/project-assignment']);
  }

  isLeadOrManager(): boolean {
    return this.userRole === 'LEAD' || this.userRole === 'MANAGER' || this.userRole === 'ADMIN_OPS_MANAGER';
  }

  viewRejectionComment(entry: TimeEntry): void {
    const comment = entry.rejectionComment;
    if (comment) {
      this.dialog.open(ConfirmationDialogComponent, {
        width: '400px',
        data: {
          title: 'Rejection Comment',
          message: comment,
          showCancel: false,
          confirmText: 'OK',
          showConfirm: true
        }
      });
    }
  }

  cloneTimeEntry(entry: TimeEntry): void {
    const clonedEntry = {
      ...entry,
      id: undefined,
      status: 'PENDING',
      date: new Date(),
      entryDate: new Date(),
      timeInMins: entry.timeInMins || (entry.hours ? entry.hours * 60 : 0),
      comment: entry.comment || '',
    };

    const dialogRef = this.dialog.open(TimeEntryFormComponent, {
      width: '800px',
      data: {
        isEditMode: false,
        timeEntry: clonedEntry,
        isCloning: true,
        existingEntries: this.dataSource.data  // Pass current time entries
      }
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.loadTimeEntries();
      }
    });
  }

  /**
   * Opens the week copy dialog to copy a time entry to multiple days in a week
   * @param entry The time entry to copy
   */
  copyToWeek(entry: TimeEntry): void {
    const dialogRef = this.dialog.open(WeekCopyDialogComponent, {
      width: '600px',
      data: {
        timeEntry: entry
      }
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        // Create a batch time entry request
        const batchRequest = {
          sourceEntry: {
            projectId: entry.projectId,
            entryDate: entry.entryDate || entry.date,
            process: entry.process,
            activity: entry.activity,
            timeInMins: entry.timeInMins || 0,
            attendanceType: entry.attendanceType,
            leadId: entry.leadId,
            comment: entry.comment || '',
            ldap: entry.ldap || '',
            isOvertime: entry.isOvertime || false
          },
          targetDates: result.selectedDates
        };

        // Send the batch request to the server
        this.http.post<BaseResponse<any>>(`${this.baseUrl}/api/time-entries/batch`, batchRequest)
          .subscribe({
            next: (response) => {
              if (response.status === 'success') {
                this.notificationService.showNotification({
                  type: 'success',
                  message: response.message || `Successfully copied time entry to ${result.selectedDates.length} days!`
                });
                this.loadTimeEntries();
              } else {
                this.notificationService.showNotification({
                  type: 'error',
                  message: response.message || 'Failed to copy time entries'
                });
              }
            },
            error: (error) => {
              console.error('Error creating batch time entries:', error);

              // Try to extract message from backend error response
              let errorMessage = 'Failed to copy time entries. Please try again.';
              if (error.error && error.error.message) {
                errorMessage = error.error.message;
              }

              // Check if the error is related to time limit
              if (errorMessage.includes('Total time for') && errorMessage.includes('would exceed 8 hours')) {
                // Extract the date from the error message
                const dateMatch = errorMessage.match(/Total time for (\d{4}-\d{2}-\d{2})/);
                const dateStr = dateMatch ? dateMatch[1] : 'a specific date';

                this.notificationService.showNotification({
                  type: 'warning',
                  message: `Cannot copy to ${dateStr} as it already has 8 hours filled without overtime. Please use the overtime toggle if needed.`
                });
              } else {
                this.notificationService.showNotification({
                  type: 'error',
                  message: errorMessage
                });
              }
            }
          });
      }
    });
  }

  /**
   * Download time entries as CSV file
   */
  downloadCSV(): void {
    // Transform the data to a more CSV-friendly format
    const exportData = this.dataSource.data.map(item => {
      // Format date safely
      let formattedDate = '';
      if (item.date || item.entryDate) {
        const dateStr = item.date || item.entryDate;
        if (dateStr) {
          formattedDate = new Date(dateStr).toISOString().split('T')[0];
        }
      }

      return {
        'ID': item.id,
        'Date Worked': item.dateWorked || formattedDate,
        'User ID': item.userIdField || item.ldap || '',
        'Reporting Senior\'s UserID': item.reportingSeniorUserId || item.leadUsername || '',
        'Resource Name': item.resourceName || '',
        'Company': item.company || 'ABC',
        'Type': item.type || item.activity || 'Ticket',
        'Notes': item.notes || item.comment || '',
        'Internal Notes': item.internalNotes || '',
        'Hours Worked': item.hoursWorked || (item.timeInMins ? (item.timeInMins / 60).toFixed(2) : '0'),
        'Billable Hours Worked': item.billableHoursWorked || (item.timeInMins ? (item.timeInMins / 60).toFixed(2) : '0'),
        'Status': item.status || ''
      };
    });

    const worksheet = XLSX.utils.json_to_sheet(exportData);
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, 'Time Entries');

    const excelBuffer: any = XLSX.write(workbook, { bookType: 'csv', type: 'array' });
    const blob = new Blob([excelBuffer], { type: 'text/csv;charset=utf-8;' });
    saveAs(blob, `time_entries_${new Date().toISOString().split('T')[0]}.csv`);

    this.notificationService.showNotification({
      type: 'success',
      message: 'Time entries exported successfully.'
    });
  }
}
