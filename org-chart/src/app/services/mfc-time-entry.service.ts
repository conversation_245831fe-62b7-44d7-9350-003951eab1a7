import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { MfcTimeEntry } from '../model/mfc-time-entry.model';

@Injectable({ providedIn: 'root' })
export class MfcTimeEntryService {
  private baseUrl = '/api/mfc-time-entries';

  constructor(private http: HttpClient) {}

  create(entry: Partial<MfcTimeEntry>): Observable<MfcTimeEntry> {
    return this.http.post<MfcTimeEntry>(this.baseUrl, entry);
  }

  update(id: number, entry: Partial<MfcTimeEntry>): Observable<MfcTimeEntry> {
    return this.http.put<MfcTimeEntry>(`${this.baseUrl}/${id}`, entry);
  }

  delete(id: number): Observable<void> {
    return this.http.delete<void>(`${this.baseUrl}/${id}`);
  }

  getMyEntries(params?: any): Observable<MfcTimeEntry[]> {
    return this.http.get<MfcTimeEntry[]>(`${this.baseUrl}/my`, { params });
  }

  getTeamEntries(params?: any): Observable<MfcTimeEntry[]> {
    return this.http.get<MfcTimeEntry[]>(`${this.baseUrl}/team`, { params });
  }

  getAllEntries(params?: any): Observable<MfcTimeEntry[]> {
    return this.http.get<MfcTimeEntry[]>(`${this.baseUrl}/all`, { params });
  }

  getById(id: number): Observable<MfcTimeEntry> {
    return this.http.get<MfcTimeEntry>(`${this.baseUrl}/${id}`);
  }

  approve(id: number): Observable<MfcTimeEntry> {
    return this.http.post<MfcTimeEntry>(`${this.baseUrl}/${id}/approve`, {});
  }

  reject(id: number, reason: string): Observable<MfcTimeEntry> {
    return this.http.post<MfcTimeEntry>(`${this.baseUrl}/${id}/reject?reason=${encodeURIComponent(reason)}`, {});
  }
} 