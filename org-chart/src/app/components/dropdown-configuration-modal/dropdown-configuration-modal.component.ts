import { Component, Inject, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { MatTableDataSource } from '@angular/material/table';
import { DropdownConfigurationService } from '../../services/dropdown-configuration.service';
import { NotificationService } from '../../shared/notification.service';
import { DropdownConfiguration, CreateDropdownConfiguration, UpdateDropdownConfiguration } from '../../model/dropdown-configuration.interface';

export interface DropdownConfigModalData {
  dropdownType: string;
  title: string;
}

@Component({
  selector: 'app-dropdown-configuration-modal',
  templateUrl: './dropdown-configuration-modal.component.html',
  styleUrls: ['./dropdown-configuration-modal.component.css']
})
export class DropdownConfigurationModalComponent implements OnInit {
  dropdownForm!: FormGroup;
  dataSource = new MatTableDataSource<DropdownConfiguration>();
  displayedColumns: string[] = ['displayName', 'optionValue', 'isActive', 'sortOrder', 'actions'];
  isLoading = false;
  isEditMode = false;
  editingItem: DropdownConfiguration | null = null;

  constructor(
    private fb: FormBuilder,
    private dropdownService: DropdownConfigurationService,
    private notificationService: NotificationService,
    public dialogRef: MatDialogRef<DropdownConfigurationModalComponent>,
    @Inject(MAT_DIALOG_DATA) public data: DropdownConfigModalData
  ) {
    this.initializeForm();
  }

  ngOnInit(): void {
    this.loadDropdownOptions();
  }

  private initializeForm(): void {
    this.dropdownForm = this.fb.group({
      optionValue: ['', [Validators.required, Validators.maxLength(100)]],
      displayName: ['', [Validators.required, Validators.maxLength(100)]],
      isActive: [true, Validators.required],
      sortOrder: [0, [Validators.min(0)]]
    });
  }

  private loadDropdownOptions(): void {
    this.isLoading = true;
    this.dropdownService.getAllDropdownOptions(this.data.dropdownType).subscribe({
      next: (options) => {
        this.dataSource.data = options;
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error loading dropdown options:', error);
        this.notificationService.showNotification({
          type: 'error',
          message: 'Failed to load dropdown options'
        });
        this.isLoading = false;
      }
    });
  }

  onSubmit(): void {
    if (this.dropdownForm.valid) {
      const formData = this.dropdownForm.value;
      
      if (this.isEditMode && this.editingItem) {
        this.updateDropdownOption(formData);
      } else {
        this.createDropdownOption(formData);
      }
    }
  }

  private createDropdownOption(formData: any): void {
    const createData: CreateDropdownConfiguration = {
      dropdownType: this.data.dropdownType,
      optionValue: formData.optionValue.trim(),
      displayName: formData.displayName.trim(),
      isActive: formData.isActive,
      sortOrder: formData.sortOrder || 0
    };

    this.dropdownService.createDropdownOption(createData).subscribe({
      next: (newOption) => {
        this.notificationService.showNotification({
          type: 'success',
          message: 'Dropdown option created successfully'
        });
        this.resetForm();
        this.loadDropdownOptions();
      },
      error: (error) => {
        console.error('Error creating dropdown option:', error);
        this.notificationService.showNotification({
          type: 'error',
          message: error.error?.message || 'Failed to create dropdown option'
        });
      }
    });
  }

  private updateDropdownOption(formData: any): void {
    if (!this.editingItem) return;

    const updateData: UpdateDropdownConfiguration = {
      displayName: formData.displayName.trim(),
      isActive: formData.isActive,
      sortOrder: formData.sortOrder
    };

    this.dropdownService.updateDropdownOption(this.editingItem.id, updateData).subscribe({
      next: (updatedOption) => {
        this.notificationService.showNotification({
          type: 'success',
          message: 'Dropdown option updated successfully'
        });
        this.resetForm();
        this.loadDropdownOptions();
      },
      error: (error) => {
        console.error('Error updating dropdown option:', error);
        this.notificationService.showNotification({
          type: 'error',
          message: error.error?.message || 'Failed to update dropdown option'
        });
      }
    });
  }

  editItem(item: DropdownConfiguration): void {
    this.isEditMode = true;
    this.editingItem = item;
    this.dropdownForm.patchValue({
      optionValue: item.optionValue,
      displayName: item.displayName,
      isActive: item.isActive,
      sortOrder: item.sortOrder
    });
    // Disable optionValue field in edit mode
    this.dropdownForm.get('optionValue')?.disable();
  }

  deleteItem(item: DropdownConfiguration): void {
    if (confirm(`Are you sure you want to delete "${item.displayName}"?`)) {
      this.dropdownService.deleteDropdownOption(item.id).subscribe({
        next: () => {
          this.notificationService.showNotification({
            type: 'success',
            message: 'Dropdown option deleted successfully'
          });
          this.loadDropdownOptions();
        },
        error: (error) => {
          console.error('Error deleting dropdown option:', error);
          this.notificationService.showNotification({
            type: 'error',
            message: error.error?.message || 'Failed to delete dropdown option'
          });
        }
      });
    }
  }

  resetForm(): void {
    this.isEditMode = false;
    this.editingItem = null;
    this.dropdownForm.reset({
      optionValue: '',
      displayName: '',
      isActive: true,
      sortOrder: 0
    });
    this.dropdownForm.get('optionValue')?.enable();
  }

  onCancel(): void {
    this.resetForm();
  }

  onClose(): void {
    this.dialogRef.close();
  }

  getStatusText(isActive: boolean): string {
    return isActive ? 'Active' : 'Inactive';
  }

  getStatusClass(isActive: boolean): string {
    return isActive ? 'status-active' : 'status-inactive';
  }
}
