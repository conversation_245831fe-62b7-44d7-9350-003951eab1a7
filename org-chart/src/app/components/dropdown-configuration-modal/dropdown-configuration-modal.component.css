.dropdown-config-modal {
  width: 100%;
  max-width: 900px;
  min-height: 600px;
  background: #ffffff;
  border-radius: 8px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #e0e0e0;
  margin: -24px -24px 0 -24px;
  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
  border-radius: 8px 8px 0 0;
}

.modal-header h2 {
  margin: 0;
  color: #1a1a1a;
  font-weight: 600;
  font-size: 20px;
  letter-spacing: -0.02em;
}

.close-button {
  color: #666;
  transition: all 0.2s ease;
}

.close-button:hover {
  color: #333;
  background-color: rgba(0, 0, 0, 0.04);
  transform: scale(1.05);
}

.modal-content {
  padding: 24px 0;
  max-height: 70vh;
  overflow-y: auto;
}

.form-section {
  margin-bottom: 32px;
  padding: 24px;
  background: #fafbfc;
  border-radius: 8px;
  border: 1px solid #e8eaed;
  transition: all 0.2s ease;
}

.form-section:hover {
  border-color: #dadce0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.form-section h3 {
  margin: 0 0 20px 0;
  color: #1a1a1a;
  font-weight: 600;
  font-size: 18px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.form-section h3::before {
  content: '';
  width: 4px;
  height: 20px;
  background: linear-gradient(135deg, #1976d2, #42a5f5);
  border-radius: 2px;
}

.dropdown-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.form-row {
  display: flex;
  gap: 20px;
  align-items: flex-start;
}

.form-field {
  flex: 1;
}

.checkbox-field {
  display: flex;
  align-items: center;
  margin-top: 12px;
  min-width: 120px;
  padding: 8px 12px;
  background: #ffffff;
  border-radius: 6px;
  border: 1px solid #e0e0e0;
}

.form-actions {
  display: flex;
  gap: 12px;
  margin-top: 20px;
  justify-content: flex-end;
}

.list-section h3 {
  margin: 0 0 20px 0;
  color: #1a1a1a;
  font-weight: 600;
  font-size: 18px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.list-section h3::before {
  content: '';
  width: 4px;
  height: 20px;
  background: linear-gradient(135deg, #4caf50, #66bb6a);
  border-radius: 2px;
}

.table-container {
  border: 1px solid #e8eaed;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  background: #ffffff;
}

.options-table {
  width: 100%;
}

.options-table th {
  background: linear-gradient(135deg, #f8f9fa 0%, #f1f3f4 100%);
  font-weight: 600;
  color: #1a1a1a;
  text-transform: uppercase;
  font-size: 12px;
  letter-spacing: 0.5px;
}

.options-table td,
.options-table th {
  padding: 16px 20px;
  border-bottom: 1px solid #f0f0f0;
  transition: background-color 0.2s ease;
}

.options-table tbody tr:hover {
  background-color: #f8f9fa;
}

.status-active {
  color: #4caf50;
  font-weight: 600;
  background: rgba(76, 175, 80, 0.1);
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.status-inactive {
  color: #f44336;
  font-weight: 600;
  background: rgba(244, 67, 54, 0.1);
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.delete-button {
  color: #f44336;
  transition: all 0.2s ease;
}

.delete-button:hover {
  background-color: rgba(244, 67, 54, 0.1);
  transform: scale(1.05);
}

.no-data {
  padding: 32px;
  text-align: center;
  color: #666;
  font-style: italic;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 32px;
  gap: 16px;
}

.loading-container p {
  margin: 0;
  color: #666;
}

.modal-actions {
  padding: 16px 24px;
  border-top: 1px solid #e0e0e0;
  margin: 0 -24px -24px -24px;
  justify-content: flex-end;
}

/* Responsive design */
@media (max-width: 600px) {
  .dropdown-config-modal {
    max-width: 100%;
    min-height: auto;
  }
  
  .form-row {
    flex-direction: column;
    gap: 8px;
  }
  
  .checkbox-field {
    margin-top: 0;
  }
  
  .modal-content {
    max-height: 60vh;
  }
  
  .options-table td,
  .options-table th {
    padding: 8px 12px;
    font-size: 14px;
  }
}

/* Material Design overrides */
::ng-deep .mat-dialog-container {
  padding: 24px;
  border-radius: 12px;
  box-shadow: 0 16px 48px rgba(0, 0, 0, 0.15);
}

::ng-deep .mat-form-field-appearance-outline .mat-form-field-outline {
  color: #e0e0e0;
  border-radius: 8px;
}

::ng-deep .mat-form-field-appearance-outline.mat-focused .mat-form-field-outline-thick {
  color: #1976d2;
  border-width: 2px;
}

::ng-deep .mat-form-field-appearance-outline .mat-form-field-label {
  color: #666;
  font-weight: 500;
}

::ng-deep .mat-checkbox-checked .mat-checkbox-background {
  background-color: #1976d2;
}

::ng-deep .mat-raised-button.mat-primary {
  background: linear-gradient(135deg, #1976d2, #42a5f5);
  color: white;
  border-radius: 8px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  box-shadow: 0 4px 12px rgba(25, 118, 210, 0.3);
  transition: all 0.2s ease;
}

::ng-deep .mat-raised-button.mat-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(25, 118, 210, 0.4);
}

::ng-deep .mat-stroked-button {
  border-radius: 8px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  transition: all 0.2s ease;
}

::ng-deep .mat-stroked-button:hover {
  background-color: rgba(0, 0, 0, 0.04);
  transform: translateY(-1px);
}
