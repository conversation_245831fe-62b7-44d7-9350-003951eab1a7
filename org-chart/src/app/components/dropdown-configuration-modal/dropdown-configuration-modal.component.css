.dropdown-config-modal {
  width: 100%;
  max-width: 800px;
  min-height: 600px;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  border-bottom: 1px solid #e0e0e0;
  margin: -24px -24px 0 -24px;
}

.modal-header h2 {
  margin: 0;
  color: #333;
  font-weight: 500;
}

.close-button {
  color: #666;
}

.modal-content {
  padding: 24px 0;
  max-height: 70vh;
  overflow-y: auto;
}

.form-section {
  margin-bottom: 32px;
  padding-bottom: 24px;
  border-bottom: 1px solid #e0e0e0;
}

.form-section h3 {
  margin: 0 0 16px 0;
  color: #333;
  font-weight: 500;
  font-size: 18px;
}

.dropdown-form {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.form-row {
  display: flex;
  gap: 16px;
  align-items: flex-start;
}

.form-field {
  flex: 1;
}

.checkbox-field {
  display: flex;
  align-items: center;
  margin-top: 8px;
  min-width: 120px;
}

.form-actions {
  display: flex;
  gap: 12px;
  margin-top: 16px;
}

.list-section h3 {
  margin: 0 0 16px 0;
  color: #333;
  font-weight: 500;
  font-size: 18px;
}

.table-container {
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  overflow: hidden;
}

.options-table {
  width: 100%;
}

.options-table th {
  background-color: #f5f5f5;
  font-weight: 600;
  color: #333;
}

.options-table td,
.options-table th {
  padding: 12px 16px;
  border-bottom: 1px solid #e0e0e0;
}

.status-active {
  color: #4caf50;
  font-weight: 500;
}

.status-inactive {
  color: #f44336;
  font-weight: 500;
}

.delete-button {
  color: #f44336;
}

.delete-button:hover {
  background-color: rgba(244, 67, 54, 0.1);
}

.no-data {
  padding: 32px;
  text-align: center;
  color: #666;
  font-style: italic;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 32px;
  gap: 16px;
}

.loading-container p {
  margin: 0;
  color: #666;
}

.modal-actions {
  padding: 16px 24px;
  border-top: 1px solid #e0e0e0;
  margin: 0 -24px -24px -24px;
  justify-content: flex-end;
}

/* Responsive design */
@media (max-width: 600px) {
  .dropdown-config-modal {
    max-width: 100%;
    min-height: auto;
  }
  
  .form-row {
    flex-direction: column;
    gap: 8px;
  }
  
  .checkbox-field {
    margin-top: 0;
  }
  
  .modal-content {
    max-height: 60vh;
  }
  
  .options-table td,
  .options-table th {
    padding: 8px 12px;
    font-size: 14px;
  }
}

/* Material Design overrides */
::ng-deep .mat-dialog-container {
  padding: 24px;
}

::ng-deep .mat-form-field-appearance-outline .mat-form-field-outline {
  color: #e0e0e0;
}

::ng-deep .mat-form-field-appearance-outline.mat-focused .mat-form-field-outline-thick {
  color: #1976d2;
}

::ng-deep .mat-checkbox-checked .mat-checkbox-background {
  background-color: #1976d2;
}

::ng-deep .mat-raised-button.mat-primary {
  background-color: #1976d2;
  color: white;
}
