# These are some examples of commonly ignored file patterns.
# You should customize this list as applicable to your project.
# Learn more about .gitignore:
#     https://www.atlassian.com/git/tutorials/saving-changes/gitignore

# Node artifact files
node_modules/
dist/
.angular/


# Compiled Java class files
*.class

# Compiled Python bytecode
*.py[cod]

# Log files
*.log

# Package files
*.jar

# Maven
target/
dist/

# JetBrains IDE
.idea/

# Unit test reports
TEST*.xml

# Generated by MacOS
.DS_Store

# Generated by Windows
Thumbs.db

# Applications
*.app
*.exe
*.war

# Large media files
*.mp4
*.tiff
*.avi
*.flv
*.mov
*.wmv




# Ignore environment files
.env
.env.prod

# Ignore swap/temp files
*.swp
*.swo
*.bak
*.backup

# Ignore database dumps and conversions
*.dump
*.sql
db*
db1*

# Ignore backup config/data files
*.backup
*.bkp
*.json.backup
*.java.backup

# Ignore shell scripts (if not required for repo)
*.sh

# Ignore build and Docker-related artifacts
build/
docker/
Dockerfile
docker-compose.yml
docker-compose.prod.yml

# Ignore test artifacts
TestVideo-*

# Ignore template/user import CSVs
*_Role_Template.csv
employee-requests.json.backup

# Ignore meltano setup & ops files
setup-meltano.sh
ops-excellence-*.json

# Ignore deployment and VM scripts
deploy-to-vm.sh
vm-setup.sh
vm_cleanup.sh
vm-deployment-script.sh
connect-vm.sh
quick-connect.sh
quick-start-docker.sh
simple-deploy-test.sh
device-identity-reset.sh
hardware-spoof.sh
vpn-proxy-setup.sh

# Ignore SQL migration and patch scripts
*.sql
quick_fix.sql
oauth_migration.sql

# Ignore team test automation folder
teamsphere-test-automation/

# Ignore all README files if not maintained
README.md
SOLUTION_README.md
VM-CONNECTION-README.md
DATABASE_DUMP_INFO.md

# Ignore augment-specific tools
augment-evasion-toolkit.sh



.dockerignore
org-chart/src/assets/env.docker.js
